# 记账管理系统 - 项目重构技术规范文档

## 文档信息
- **文档版本**: 1.0
- **创建日期**: 2025-08-01
- **最后更新**: 2025-08-01
- **文档目的**: 为项目重构提供完整的技术规范和实现指南

## 目录
1. [项目概述](#1-项目概述)
2. [技术架构](#2-技术架构)
3. [功能模块详解](#3-功能模块详解)
4. [计算逻辑规范](#4-计算逻辑规范)
5. [数据结构定义](#5-数据结构定义)
6. [API接口规范](#6-api接口规范)
7. [业务规则说明](#7-业务规则说明)
8. [配置参数说明](#8-配置参数说明)
9. [依赖关系图](#9-依赖关系图)
10. [部署和运维](#10-部署和运维)

---

## 1. 项目概述

### 1.1 项目目标
现代化记账管理系统，支持多账本管理、智能记账、数据统计分析和导出功能。系统采用前后端分离架构，提供Web界面和API接口。

### 1.2 主要功能
- **用户管理**: 用户注册、登录、认证
- **账本管理**: 多账本创建、编辑、删除
- **记账记录**: 记录创建、编辑、状态管理
- **智能计算**: 累计金额、递减形式、续期计算
- **统计分析**: 数据统计、趋势分析、图表展示
- **数据导出**: CSV、JSON格式导出
- **回收站**: 软删除和恢复机制
- **月度管理**: 月度状态跟踪和锁定

### 1.3 核心特性
- **递减记账**: 支持金额递减形式的记账方式
- **月度状态**: 每个记录在不同月份的独立状态管理
- **续期计算**: 智能续期周期计算（1/2/3/6个月）
- **实时统计**: 基于当前数据的实时计算
- **多级缓存**: 内存缓存 + LocalStorage缓存
- **性能监控**: 请求性能和资源使用监控

---

## 2. 技术架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端API       │    │   数据库        │
│                 │    │                 │    │                 │
│ HTML/CSS/JS     │◄──►│ PHP API         │◄──►│ MySQL          │
│ Vue.js          │    │ RESTful         │    │ InnoDB          │
│ Element Plus    │    │ JWT Auth        │    │ utf8mb4         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   缓存层        │    │   监控层        │    │   日志系统      │
│                 │    │                 │    │                 │
│ Memory Cache    │    │ Performance     │    │ File Logging    │
│ LocalStorage    │    │ Error Monitor   │    │ Error Tracking  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 技术栈详情

#### 前端技术栈
- **核心框架**: Vue.js 3.3.0
- **UI组件库**: Element Plus 2.3.0
- **构建工具**: Vite 4.3.0
- **状态管理**: Pinia 2.1.0
- **路由管理**: Vue Router 4.2.0
- **HTTP客户端**: Axios 1.4.0
- **日期处理**: Day.js 1.11.0
- **工具库**: Lodash-es 4.17.0
- **开发语言**: TypeScript 5.1.0

#### 后端技术栈
- **开发语言**: PHP 8.0+
- **数据库**: MySQL 8.0+
- **认证方式**: JWT (JSON Web Token)
- **API风格**: RESTful API
- **数据库引擎**: InnoDB
- **字符集**: utf8mb4

#### 开发工具
- **代码检查**: ESLint + Vue ESLint Plugin
- **测试框架**: Vitest + Vue Test Utils
- **样式预处理**: Sass/SCSS
- **自动导入**: unplugin-auto-import
- **组件自动导入**: unplugin-vue-components

---

## 3. 功能模块详解

### 3.1 用户认证模块

#### 3.1.1 功能描述
负责用户注册、登录、JWT token管理和权限验证。

#### 3.1.2 核心流程
1. **用户注册**:
   - 输入: username, email, password
   - 验证: 用户名唯一性、邮箱格式、密码强度
   - 处理: 密码哈希、创建用户、生成默认账本
   - 输出: JWT token + 用户信息

2. **用户登录**:
   - 输入: username/email, password
   - 验证: 用户存在性、密码正确性
   - 处理: 生成JWT token
   - 输出: JWT token + 用户信息

3. **Token验证**:
   - 输入: Authorization Header
   - 验证: Token有效性、过期时间
   - 处理: 解析用户信息
   - 输出: 用户身份信息

#### 3.1.3 安全机制
- 密码使用 `password_hash()` 进行bcrypt哈希
- JWT token 7天有效期
- 支持token刷新机制
- SQL预处理防注入
- XSS过滤和CORS控制

### 3.2 账本管理模块

#### 3.2.1 功能描述
管理用户的多个账本，支持创建、编辑、删除和权限控制。

#### 3.2.2 核心流程
1. **创建账本**:
   - 输入: name, description
   - 验证: 名称非空、用户权限
   - 处理: 插入数据库、关联用户
   - 输出: 账本信息

2. **编辑账本**:
   - 输入: book_id, name, description
   - 验证: 账本存在性、用户权限
   - 处理: 更新数据库记录
   - 输出: 更新后账本信息

3. **删除账本**:
   - 输入: book_id
   - 验证: 账本存在性、用户权限、非最后账本
   - 处理: 记录移至回收站、删除账本
   - 输出: 删除结果和移动记录数

#### 3.2.3 特殊规则
- 用户至少保留一个账本
- 删除账本时记录自动移至回收站
- 支持回收站账本自动创建
- 账本名称在用户范围内唯一

### 3.3 记账记录模块

#### 3.3.1 功能描述
管理记账记录的创建、编辑、状态管理和计算逻辑。

#### 3.3.2 数据字段说明
- `amount`: 原始金额（总金额）
- `monthly_amount`: 每月金额
- `renewal_amount`: 续期金额
- `renewal_time`: 续期周期（一个月/二个月/三个月/六个月/永久）
- `accumulated_amount`: 累计金额
- `is_decreasing`: 是否为递减形式
- `remaining_amount`: 剩余金额（递减形式使用）
- `is_finished`: 是否已结束（递减形式清零后）

#### 3.3.3 核心流程
1. **创建记录**:
   - 输入: 所有必填字段
   - 验证: 字段完整性、数据格式、账本权限
   - 处理: 插入记录、初始化状态
   - 输出: 记录信息

2. **编辑记录**:
   - 输入: record_id + 更新字段
   - 验证: 记录存在性、用户权限
   - 处理: 更新记录、重新计算相关金额
   - 输出: 更新后记录信息

3. **状态管理**:
   - 月度状态独立管理
   - 支持历史月份状态查看
   - 实时计算累计金额和剩余金额

---

## 4. 计算逻辑规范

### 4.1 基础金额计算

#### 4.1.1 金额字段定义
```javascript
// 基础金额字段
const record = {
    amount: 1000.00,           // 原始总金额
    monthly_amount: 100.00,    // 每月金额
    renewal_amount: 120.00,    // 续期金额
    renewal_time: "三个月",    // 续期周期
    accumulated_amount: 300.00, // 累计金额
    remaining_amount: 700.00   // 剩余金额（递减形式）
};
```

#### 4.1.2 续期月份判断算法
```javascript
function isRenewalMonth(record, viewMonth) {
    const monthsToAdd = {
        '一个月': 1,
        '二个月': 2, 
        '三个月': 3,
        '六个月': 6
    };
    
    const addMonths = monthsToAdd[record.renewal_time];
    if (!addMonths || record.renewal_time === '永久') {
        return false;
    }
    
    const recordDate = new Date(record.date);
    const viewDate = new Date(viewMonth + '-01');
    
    // 计算月份差
    const monthDiff = (viewDate.getFullYear() - recordDate.getFullYear()) * 12 
                    + (viewDate.getMonth() - recordDate.getMonth());
    
    // 判断是否为续期月份
    return monthDiff > 0 && monthDiff % addMonths === 0;
}
```

### 4.2 累计金额计算

#### 4.2.1 计算公式
```
累计金额 = Σ(已完成月份的金额)

其中每月金额 = {
    续期金额,     如果当月为续期月份
    每月金额,     如果当月为普通月份
}
```

#### 4.2.2 实现算法
```javascript
function calculateAccumulatedAmount(recordId, currentMonth) {
    // 查询所有已完成的月份状态
    const completedMonths = getCompletedMonths(recordId, currentMonth);
    
    let totalAmount = 0;
    for (const month of completedMonths) {
        const isRenewal = isRenewalMonth(record, month);
        const amount = isRenewal ? 
            parseFloat(record.renewal_amount) : 
            parseFloat(record.monthly_amount);
        totalAmount += amount;
    }
    
    return totalAmount;
}
```

### 4.3 递减形式计算

#### 4.3.1 递减逻辑说明
递减形式记账是指随着时间推移，记录的剩余金额逐渐减少，直至清零的记账方式。

#### 4.3.2 计算公式
```
剩余金额 = 原始金额 - 累计金额
历史剩余金额 = 原始金额 - 截止到指定月份的累计金额

当剩余金额 <= 0 时，标记为已结束 (is_finished = 1)
```

#### 4.3.3 实现算法
```javascript
function calculateHistoricalRemainingAmount(record, viewMonth) {
    const originalAmount = parseFloat(record.amount || 0);
    const accumulatedAmount = parseFloat(record.accumulated_amount || 0);
    
    // 历史剩余金额 = 原始金额 - 截止到查看月份的累计金额
    const historicalRemainingAmount = originalAmount - accumulatedAmount;
    
    // 确保不为负数
    return Math.max(0, historicalRemainingAmount);
}

function updateDecreasingRecord(record, isCompleted) {
    if (record.is_decreasing == 1) {
        const isRenewal = isRenewalMonth(record, currentMonth);
        const amount = isRenewal ? 
            parseFloat(record.renewal_amount) : 
            parseFloat(record.monthly_amount);
            
        if (isCompleted) {
            // 完成时减少剩余金额
            record.remaining_amount -= amount;
            record.accumulated_amount += amount;
            
            // 检查是否结束
            if (record.remaining_amount <= 0) {
                record.remaining_amount = 0;
                record.is_finished = 1;
            }
        } else {
            // 取消完成时恢复剩余金额
            record.remaining_amount += amount;
            record.accumulated_amount = Math.max(0, record.accumulated_amount - amount);
            
            // 恢复结束状态
            if (record.remaining_amount > 0) {
                record.is_finished = 0;
            }
        }
    }
}
```

### 4.4 统计计算

#### 4.4.1 总览统计
```javascript
function calculateOverviewStats(records, currentViewMonth) {
    return records.reduce((acc, record) => {
        // 根据记录类型计算显示金额
        let displayAmount;
        if (record.is_decreasing == 1) {
            displayAmount = calculateHistoricalRemainingAmount(record, currentViewMonth);
        } else {
            displayAmount = parseFloat(record.amount || 0);
        }
        
        acc.totalAmount += displayAmount;
        acc.totalMonthlyAmount += parseFloat(record.monthly_amount || 0);
        acc.totalRenewalAmount += parseFloat(record.renewal_amount || 0);
        acc.totalAccumulatedAmount += parseFloat(record.accumulated_amount || 0);
        
        // 计算当月累积金额
        if (record.current_completed) {
            const isRenewal = isRenewalMonth(record, currentViewMonth);
            const monthlyAmount = isRenewal ?
                parseFloat(record.renewal_amount || 0) :
                parseFloat(record.monthly_amount || 0);
            acc.currentMonthAccumulated += monthlyAmount;
        }
        
        return acc;
    }, {
        totalAmount: 0,
        totalMonthlyAmount: 0,
        totalRenewalAmount: 0,
        totalAccumulatedAmount: 0,
        currentMonthAccumulated: 0
    });
}
```

---

## 5. 数据结构定义

### 5.1 数据库表结构

#### 5.1.1 用户表 (users)
```sql
CREATE TABLE `users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL UNIQUE,
    `email` varchar(100) NOT NULL UNIQUE,
    `password` varchar(255) NOT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 5.1.2 账本表 (account_books)
```sql
CREATE TABLE `account_books` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `name` varchar(100) NOT NULL,
    `description` text,
    `is_recycle_bin` tinyint(1) DEFAULT 0,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 5.1.3 记录表 (records)
```sql
CREATE TABLE `records` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `account_book_id` int(11) NOT NULL,
    `date` date NOT NULL,
    `name` varchar(100) NOT NULL,
    `amount` decimal(10,2) NOT NULL,
    `monthly_amount` decimal(10,2) NOT NULL,
    `renewal_time` varchar(50) NOT NULL,
    `renewal_amount` decimal(10,2) NOT NULL,
    `remark` text,
    `accumulated_amount` decimal(10,2) DEFAULT 0,
    `is_completed` tinyint(1) DEFAULT 0,
    `completed_month` varchar(7),
    `is_locked` tinyint(1) DEFAULT 0,
    `is_decreasing` tinyint(1) DEFAULT 0,
    `remaining_amount` decimal(10,2) DEFAULT 0,
    `is_finished` tinyint(1) DEFAULT 0,
    `original_book_id` int(11) NULL,
    `deleted_at` timestamp NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`account_book_id`) REFERENCES `account_books`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 5.1.4 月份状态表 (record_monthly_states)
```sql
CREATE TABLE `record_monthly_states` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `record_id` int(11) NOT NULL,
    `view_month` varchar(7) NOT NULL COMMENT '格式: 2024-05',
    `is_completed` tinyint(1) DEFAULT 0,
    `completed_at` timestamp NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_record_month` (`record_id`, `view_month`),
    KEY `idx_view_month` (`view_month`),
    KEY `idx_is_completed` (`is_completed`),
    FOREIGN KEY (`record_id`) REFERENCES `records`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 5.2 数据库索引优化
```sql
-- 优化账本记录查询
CREATE INDEX idx_records_book_completed ON records(account_book_id, is_completed);
CREATE INDEX idx_records_book_date ON records(account_book_id, date);
CREATE INDEX idx_records_book_decreasing ON records(account_book_id, is_decreasing);

-- 优化统计查询
CREATE INDEX idx_records_completed_month ON records(is_completed, completed_month);
CREATE INDEX idx_records_renewal_time ON records(renewal_time);

-- 优化月份状态查询
CREATE INDEX idx_record_month_optimized ON record_monthly_states(record_id, view_month, is_completed);
CREATE INDEX idx_month_state_optimized ON record_monthly_states(view_month, is_completed, record_id);
```

### 5.3 前端数据模型

#### 5.3.1 用户数据模型
```typescript
interface User {
    id: number;
    username: string;
    email: string;
    created_at: string;
    updated_at: string;
}

interface AuthResponse {
    token: string;
    user: User;
}
```

#### 5.3.2 账本数据模型
```typescript
interface AccountBook {
    id: number;
    user_id: number;
    name: string;
    description: string;
    is_recycle_bin: boolean;
    created_at: string;
    updated_at: string;
}
```

#### 5.3.3 记录数据模型
```typescript
interface Record {
    id: number;
    account_book_id: number;
    date: string;
    name: string;
    amount: number;
    monthly_amount: number;
    renewal_time: string;
    renewal_amount: number;
    remark: string;
    accumulated_amount: number;
    is_completed: boolean;
    completed_month: string;
    is_locked: boolean;
    is_decreasing: boolean;
    remaining_amount: number;
    is_finished: boolean;
    current_completed?: boolean; // 当前月份完成状态
    created_at: string;
    updated_at: string;
}
```

#### 5.3.4 统计数据模型
```typescript
interface OverviewStats {
    total_books: number;
    total_records: number;
    completed_records: number;
    total_accumulated: number;
    monthly_income: number;
    month_records: number;
    month_completed: number;
    month_income: number;
    month_accumulated: number;
}

interface MonthlyStats {
    month: string;
    total_amount: number;
    completed_amount: number;
    completion_rate: number;
}

interface TrendStats {
    date: string;
    amount: number;
    type: 'income' | 'expense';
}
```

---

## 6. API接口规范

### 6.1 认证接口

#### 6.1.1 用户注册
```
POST /api/auth/register
Content-Type: application/json

Request Body:
{
    "username": "string",
    "email": "string", 
    "password": "string"
}

Response:
{
    "success": true,
    "message": "注册成功",
    "data": {
        "token": "jwt_token_string",
        "user": {
            "id": 1,
            "username": "testuser",
            "email": "<EMAIL>"
        }
    }
}
```

#### 6.1.2 用户登录
```
POST /api/auth/login
Content-Type: application/json

Request Body:
{
    "username": "string", // 用户名或邮箱
    "password": "string"
}

Response:
{
    "success": true,
    "message": "登录成功",
    "data": {
        "token": "jwt_token_string",
        "user": {
            "id": 1,
            "username": "testuser",
            "email": "<EMAIL>"
        }
    }
}
```

### 6.2 账本管理接口

#### 6.2.1 获取账本列表
```
GET /api/account-books
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "默认账本",
            "description": "系统自动创建的默认账本",
            "is_recycle_bin": false,
            "created_at": "2024-01-01 00:00:00",
            "updated_at": "2024-01-01 00:00:00"
        }
    ]
}
```

#### 6.2.2 创建账本
```
POST /api/account-books
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
    "name": "string",
    "description": "string"
}

Response:
{
    "success": true,
    "message": "账本创建成功",
    "data": {
        "id": 2,
        "name": "新账本",
        "description": "账本描述",
        "is_recycle_bin": false,
        "created_at": "2024-01-01 00:00:00",
        "updated_at": "2024-01-01 00:00:00"
    }
}
```

### 6.3 记录管理接口

#### 6.3.1 获取记录列表
```
GET /api/records/{book_id}?month={YYYY-MM}
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": [
        {
            "id": 1,
            "account_book_id": 1,
            "date": "2024-01-01",
            "name": "记录名称",
            "amount": 1000.00,
            "monthly_amount": 100.00,
            "renewal_time": "三个月",
            "renewal_amount": 120.00,
            "remark": "备注信息",
            "accumulated_amount": 300.00,
            "is_completed": false,
            "completed_month": null,
            "is_locked": false,
            "is_decreasing": false,
            "remaining_amount": 700.00,
            "is_finished": false,
            "current_completed": false,
            "created_at": "2024-01-01 00:00:00",
            "updated_at": "2024-01-01 00:00:00"
        }
    ]
}
```

#### 6.3.2 创建记录
```
POST /api/records/{book_id}
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
    "date": "2024-01-01",
    "name": "记录名称",
    "amount": 1000.00,
    "monthly_amount": 100.00,
    "renewal_time": "三个月",
    "renewal_amount": 120.00,
    "remark": "备注信息",
    "is_decreasing": false
}

Response:
{
    "success": true,
    "message": "记录创建成功",
    "data": {
        // 完整的记录对象
    }
}
```

#### 6.3.3 更新记录状态
```
POST /api/records/{book_id}/{record_id}/toggle
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
    "month": "2024-01", // 可选，默认当前月份
    "completed": true   // 目标状态
}

Response:
{
    "success": true,
    "message": "状态更新成功",
    "data": {
        "record_id": 1,
        "month": "2024-01",
        "completed": true,
        "accumulated_amount": 400.00,
        "remaining_amount": 600.00
    }
}
```

### 6.4 统计分析接口

#### 6.4.1 获取总览统计
```
GET /api/statistics/overview
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": {
        "total_books": 3,
        "total_records": 25,
        "completed_records": 15,
        "total_accumulated": 5000.00,
        "monthly_income": 1200.00,
        "month_records": 8,
        "month_completed": 5,
        "month_income": 800.00,
        "month_accumulated": 600.00
    }
}
```

#### 6.4.2 获取月度统计
```
GET /api/statistics/monthly?year={YYYY}
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": [
        {
            "month": "2024-01",
            "total_amount": 10000.00,
            "completed_amount": 6000.00,
            "completion_rate": 0.6
        }
    ]
}
```

### 6.5 数据导出接口

#### 6.5.1 导出CSV
```
GET /api/export/csv?token={jwt_token}
Authorization: Bearer {token}

Response:
Content-Type: text/csv
Content-Disposition: attachment; filename="accounting_data_2024-01-01.csv"

账本名称,日期,名称,金额,每月金额,续期时间,续期金额,备注,累计金额,是否完成,完成月份,是否锁定,创建时间,更新时间
默认账本,2024-01-01,测试记录,1000.00,100.00,三个月,120.00,测试备注,300.00,0,,0,2024-01-01 00:00:00,2024-01-01 00:00:00
```

#### 6.5.2 导出JSON
```
GET /api/export/json?token={jwt_token}
Authorization: Bearer {token}

Response:
Content-Type: application/json
Content-Disposition: attachment; filename="accounting_data_2024-01-01.json"

{
    "export_info": {
        "timestamp": "2024-01-01 00:00:00",
        "user": {
            "username": "testuser",
            "email": "<EMAIL>"
        }
    },
    "books": [
        {
            "id": 1,
            "name": "默认账本",
            "description": "系统自动创建的默认账本",
            "records": [
                // 记录数组
            ]
        }
    ]
}
```

### 6.6 回收站接口

#### 6.6.1 获取回收站记录
```
GET /api/recycle-bin/records
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": [
        {
            // 记录对象，包含 original_book_id 和 deleted_at
            "original_book_id": 1,
            "deleted_at": "2024-01-01 00:00:00",
            "original_book_name": "原账本名称"
        }
    ]
}
```

#### 6.6.2 恢复记录
```
POST /api/recycle-bin/restore/{record_id}
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
    "target_book_id": 1 // 可选，指定目标账本
}

Response:
{
    "success": true,
    "message": "记录恢复成功",
    "data": {
        "record_id": 1,
        "target_book": "目标账本名称"
    }
}
```

---

## 7. 业务规则说明

### 7.1 用户管理规则

#### 7.1.1 注册规则
- 用户名：3-50字符，只能包含字母、数字、下划线
- 邮箱：必须是有效的邮箱格式，系统内唯一
- 密码：最少6位字符，建议包含字母和数字
- 注册成功后自动创建默认账本

#### 7.1.2 认证规则
- JWT token有效期7天
- token过期后需要重新登录
- 支持用户名或邮箱登录
- 密码错误次数限制（可扩展）

### 7.2 账本管理规则

#### 7.2.1 账本创建规则
- 账本名称在用户范围内必须唯一
- 账本名称不能为空
- 描述字段可选
- 每个用户至少保留一个账本

#### 7.2.2 账本删除规则
- 不能删除用户的最后一个账本
- 删除账本时，所有记录自动移至回收站
- 回收站账本自动创建（如果不存在）
- 回收站账本不能被删除

### 7.3 记录管理规则

#### 7.3.1 记录创建规则
- 必填字段：date, name, amount, renewal_time
- monthly_amount和renewal_amount默认为0
- renewal_time只能是：一个月、二个月、三个月、六个月、永久
- 金额字段必须为非负数，最多2位小数

#### 7.3.2 记录编辑规则
- 只有记录所有者可以编辑
- 编辑后需要重新计算相关金额
- 锁定的记录不能编辑状态
- 已结束的递减记录提示用户处理

#### 7.3.3 状态管理规则
- 每个记录在每个月份有独立的完成状态
- 当前月份的状态变更会影响累计金额
- 历史月份的状态变更不影响累计金额
- 递减记录完成时会减少剩余金额

### 7.4 计算规则

#### 7.4.1 续期计算规则
- 续期月份 = 记录日期 + N个月（N为续期周期）
- 续期月份使用renewal_amount，其他月份使用monthly_amount
- 永久续期的记录不参与续期计算

#### 7.4.2 累计金额规则
- 累计金额 = 所有已完成月份的金额总和
- 只有当前月份及之前的完成状态才计入累计金额
- 未来月份的状态不影响当前累计金额

#### 7.4.3 递减形式规则
- 剩余金额 = 原始金额 - 累计金额
- 剩余金额不能为负数
- 剩余金额为0时自动标记为已结束
- 已结束的记录不能再完成新的月份

### 7.5 权限控制规则

#### 7.5.1 数据访问权限
- 用户只能访问自己的账本和记录
- 账本所有者才能管理账本下的记录
- 回收站记录只有原所有者可以操作

#### 7.5.2 操作权限
- 创建：用户可以在自己的账本中创建记录
- 编辑：只能编辑自己的记录
- 删除：删除记录会移至回收站
- 恢复：只能恢复自己删除的记录

### 7.6 数据完整性规则

#### 7.6.1 外键约束
- 记录必须关联有效的账本
- 账本必须关联有效的用户
- 月份状态必须关联有效的记录

#### 7.6.2 数据一致性
- 累计金额必须等于所有已完成月份的金额总和
- 递减记录的剩余金额必须等于原始金额减去累计金额
- 已结束的递减记录剩余金额必须为0

---

## 8. 配置参数说明

### 8.1 数据库配置

#### 8.1.1 连接配置
```php
// 数据库连接参数
define('DB_HOST', '1Panel-mysql-dPoE');     // 数据库主机
define('DB_PORT', '3306');                   // 数据库端口
define('DB_NAME', 'shuju');                  // 数据库名称
define('DB_USER', 'shuju');                  // 数据库用户名
define('DB_PASS', 'Abc112211');              // 数据库密码
define('DB_CHARSET', 'utf8mb4');             // 字符集

// PDO连接选项
$pdoOptions = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::ATTR_TIMEOUT => 30  // 30秒超时
];
```

#### 8.1.2 环境变量配置
```bash
# .env 文件配置
DB_HOST=1Panel-mysql-dPoE
DB_PORT=3306
DB_NAME=shuju
DB_USER=shuju
DB_PASS=your_secure_password_here
DB_CHARSET=utf8mb4
```

### 8.2 JWT配置

#### 8.2.1 JWT参数
```php
// JWT配置
define('JWT_SECRET', 'your-secret-key-change-this-in-production');
define('JWT_ALGORITHM', 'HS256');

// JWT payload结构
$payload = [
    'user_id' => $userId,
    'username' => $username,
    'exp' => time() + (7 * 24 * 60 * 60)  // 7天过期
];
```

#### 8.2.2 安全建议
- JWT_SECRET应该是至少32个字符的随机字符串
- 生产环境中必须更改默认密钥
- 定期轮换JWT密钥以提高安全性

### 8.3 性能配置

#### 8.3.1 PHP性能参数
```php
// 性能优化设置
ini_set('memory_limit', '128M');        // 内存限制
set_time_limit(30);                     // 执行时间限制
ob_start('ob_gzhandler');              // 启用gzip压缩
```

#### 8.3.2 缓存配置
```javascript
// 前端缓存配置
const CACHE_CONFIG = {
    // 用户数据缓存
    user: {
        l1MaxSize: 10,
        l1TTL: 5 * 60 * 1000,      // 5分钟
        l2TTL: 60 * 60 * 1000      // 1小时
    },
    
    // 账本数据缓存
    books: {
        l1MaxSize: 20,
        l1TTL: 3 * 60 * 1000,      // 3分钟
        l2TTL: 30 * 60 * 1000      // 30分钟
    },
    
    // 记录数据缓存
    records: {
        l1MaxSize: 30,
        l1TTL: 2 * 60 * 1000,      // 2分钟
        l2TTL: 15 * 60 * 1000      // 15分钟
    }
};
```

#### 8.3.3 后端查询缓存
```php
// 查询缓存配置
class QueryCache {
    private static $ttl = 300;  // 5分钟TTL
    
    // 缓存键生成规则
    private static function generateKey($sql, $params) {
        return md5($sql . serialize($params));
    }
}
```

### 8.4 日志配置

#### 8.4.1 日志级别
```php
// 日志级别定义
const LEVEL_DEBUG = 'DEBUG';
const LEVEL_INFO = 'INFO';
const LEVEL_WARNING = 'WARNING';
const LEVEL_ERROR = 'ERROR';
const LEVEL_CRITICAL = 'CRITICAL';
```

#### 8.4.2 日志文件管理
```php
// 日志配置
$logConfig = [
    'log_dir' => __DIR__ . '/../logs/',
    'max_file_size' => 10 * 1024 * 1024,  // 10MB
    'retention_days' => 30,                // 保留30天
    'error_log_separate' => true           // 错误日志单独文件
];
```

### 8.5 安全配置

#### 8.5.1 CORS配置
```php
// 跨域配置
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
```

#### 8.5.2 安全头配置
```php
// 安全响应头
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
```

### 8.6 监控配置

#### 8.6.1 性能监控
```php
// 性能监控配置
$monitoringConfig = [
    'slow_query_threshold' => 1.0,        // 慢查询阈值（秒）
    'memory_usage_threshold' => 0.8,      // 内存使用阈值（80%）
    'response_time_threshold' => 2.0,     // 响应时间阈值（秒）
    'enable_profiling' => false           // 是否启用性能分析
];
```

#### 8.6.2 错误监控
```php
// 错误监控配置
$errorConfig = [
    'error_reporting' => E_ALL & ~E_NOTICE,
    'log_errors' => true,
    'display_errors' => false,            // 生产环境关闭
    'error_log' => '/path/to/error.log'
];
```

### 8.7 备份配置

#### 8.7.1 自动备份配置
```php
// 备份配置
$backupConfig = [
    'backup_dir' => __DIR__ . '/Backup',
    'database' => [
        'host' => '1Panel-mysql-dPoE',
        'port' => '3306',
        'name' => 'shuju',
        'user' => 'shuju',
        'pass' => 'Abc112211'
    ],
    'exclude_patterns' => [
        'Backup/*',
        'logs/*',
        '.git/*',
        'node_modules/*',
        '*.tmp',
        '*.log'
    ],
    'retention_days' => 7,                // 保留7天备份
    'compression' => true                 // 启用压缩
];
```

---

## 9. 依赖关系图

### 9.1 系统架构依赖图

```mermaid
graph TB
    subgraph "前端层"
        A[Vue.js应用] --> B[Element Plus UI]
        A --> C[Pinia状态管理]
        A --> D[Vue Router路由]
        A --> E[Axios HTTP客户端]
    end
    
    subgraph "API层"
        F[PHP API服务] --> G[JWT认证中间件]
        F --> H[路由分发器]
        F --> I[业务逻辑层]
    end
    
    subgraph "数据层"
        J[MySQL数据库] --> K[用户表]
        J --> L[账本表]
        J --> M[记录表]
        J --> N[月份状态表]
    end
    
    subgraph "缓存层"
        O[内存缓存] --> P[查询缓存]
        Q[LocalStorage] --> R[前端缓存]
    end
    
    subgraph "监控层"
        S[性能监控] --> T[日志系统]
        S --> U[错误追踪]
    end
    
    A --> F
    F --> J
    F --> O
    F --> S
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style J fill:#e8f5e8
    style O fill:#fff3e0
    style S fill:#fce4ec
```

### 9.2 模块依赖关系图

```mermaid
graph LR
    subgraph "认证模块"
        A1[用户注册] --> A2[JWT生成]
        A3[用户登录] --> A2
        A2 --> A4[Token验证]
    end
    
    subgraph "账本模块"
        B1[账本创建] --> B2[权限验证]
        B3[账本编辑] --> B2
        B4[账本删除] --> B5[记录迁移]
        B5 --> C1[回收站]
    end
    
    subgraph "记录模块"
        C2[记录创建] --> C3[状态初始化]
        C4[记录编辑] --> C5[金额重算]
        C6[状态切换] --> C7[累计金额更新]
        C7 --> C8[递减逻辑处理]
    end
    
    subgraph "统计模块"
        D1[数据查询] --> D2[实时计算]
        D2 --> D3[缓存更新]
        D3 --> D4[结果返回]
    end
    
    subgraph "导出模块"
        E1[数据提取] --> E2[格式转换]
        E2 --> E3[文件生成]
    end
    
    A4 --> B2
    A4 --> C2
    A4 --> D1
    A4 --> E1
    
    B1 --> C2
    C7 --> D2
    
    style A1 fill:#ffebee
    style B1 fill:#e8f5e8
    style C2 fill:#e3f2fd
    style D1 fill:#f3e5f5
    style E1 fill:#fff8e1
```

### 9.3 数据流向图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端应用
    participant A as API服务
    participant D as 数据库
    participant C as 缓存层
    
    U->>F: 登录请求
    F->>A: POST /api/auth/login
    A->>D: 验证用户凭据
    D-->>A: 用户信息
    A-->>F: JWT Token
    F-->>U: 登录成功
    
    U->>F: 查看记录
    F->>C: 检查缓存
    alt 缓存命中
        C-->>F: 返回缓存数据
    else 缓存未命中
        F->>A: GET /api/records/{book_id}
        A->>D: 查询记录数据
        D-->>A: 记录列表
        A->>A: 计算累计金额
        A-->>F: 处理后数据
        F->>C: 更新缓存
    end
    F-->>U: 显示记录列表
    
    U->>F: 切换记录状态
    F->>A: POST /api/records/{id}/toggle
    A->>D: 更新月份状态
    A->>D: 重新计算累计金额
    A->>D: 更新记录信息
    D-->>A: 更新结果
    A-->>F: 返回新状态
    F->>C: 清除相关缓存
    F-->>U: 状态更新成功
```

### 9.4 计算逻辑依赖图

```mermaid
graph TD
    A[记录基础数据] --> B[续期月份判断]
    A --> C[月份状态查询]
    
    B --> D[金额类型选择]
    C --> D
    
    D --> E[每月金额计算]
    D --> F[续期金额计算]
    
    E --> G[累计金额计算]
    F --> G
    
    G --> H{是否递减形式?}
    
    H -->|是| I[剩余金额计算]
    H -->|否| J[显示原始金额]
    
    I --> K{剩余金额 <= 0?}
    K -->|是| L[标记为已结束]
    K -->|否| M[继续递减]
    
    G --> N[统计数据汇总]
    J --> N
    I --> N
    
    style A fill:#e1f5fe
    style G fill:#e8f5e8
    style H fill:#fff3e0
    style K fill:#fff3e0
    style N fill:#f3e5f5
```

---

## 10. 部署和运维

### 10.1 环境要求

#### 10.1.1 服务器环境
- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 8+)
- **Web服务器**: Nginx 1.18+ 或 Apache 2.4+
- **PHP版本**: PHP 8.0+
- **数据库**: MySQL 8.0+ 或 MariaDB 10.5+
- **内存**: 最少2GB，推荐4GB+
- **存储**: 最少10GB可用空间

#### 10.1.2 PHP扩展要求
```bash
# 必需的PHP扩展
php-mysql
php-json
php-mbstring
php-curl
php-xml
php-zip
php-gd
php-intl
```

### 10.2 部署步骤

#### 10.2.1 代码部署
```bash
# 1. 克隆代码仓库
git clone <repository-url> /var/www/accounting-system
cd /var/www/accounting-system

# 2. 设置文件权限
chown -R www-data:www-data /var/www/accounting-system
chmod -R 755 /var/www/accounting-system
chmod -R 777 /var/www/accounting-system/logs
chmod -R 777 /var/www/accounting-system/Backup

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置数据库连接等配置
```

#### 10.2.2 数据库初始化
```bash
# 1. 创建数据库
mysql -u root -p
CREATE DATABASE shuju CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'shuju'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON shuju.* TO 'shuju'@'localhost';
FLUSH PRIVILEGES;

# 2. 导入数据库结构
mysql -u shuju -p shuju < backend/database/init.sql

# 3. 优化数据库索引
mysql -u shuju -p shuju < backend/database/optimize_indexes.sql
```

#### 10.2.3 前端构建
```bash
# 1. 安装Node.js依赖
cd vue-app
npm install

# 2. 构建生产版本
npm run build

# 3. 部署构建文件
cp -r dist/* /var/www/accounting-system/public/
```

### 10.3 Web服务器配置

#### 10.3.1 Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/accounting-system;
    index index.html index.php;
    
    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API路由
    location /api/ {
        try_files $uri $uri/ /backend/api/index.php?$query_string;
    }
    
    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 安全配置
    location ~ /\. {
        deny all;
    }
    
    location ~ /(logs|Backup|backend/config)/ {
        deny all;
    }
}
```

#### 10.3.2 Apache配置
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/accounting-system
    
    # 前端路由支持
    <Directory "/var/www/accounting-system">
        AllowOverride All
        Require all granted
        
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # API路由
    Alias /api /var/www/accounting-system/backend/api
    <Directory "/var/www/accounting-system/backend/api">
        AllowOverride All
        Require all granted
    </Directory>
    
    # 安全配置
    <DirectoryMatch "/(logs|Backup|backend/config)/">
        Require all denied
    </DirectoryMatch>
</VirtualHost>
```

### 10.4 监控和维护

#### 10.4.1 日志监控
```bash
# 1. 设置日志轮转
cat > /etc/logrotate.d/accounting-system << EOF
/var/www/accounting-system/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
EOF

# 2. 监控错误日志
tail -f /var/www/accounting-system/logs/error.log

# 3. 性能日志分析
grep "Slow" /var/www/accounting-system/logs/performance.log
```

#### 10.4.2 数据库维护
```sql
-- 1. 定期优化表
OPTIMIZE TABLE users, account_books, records, record_monthly_states;

-- 2. 检查索引使用情况
SHOW INDEX FROM records;

-- 3. 监控慢查询
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;
```

#### 10.4.3 备份策略
```bash
# 1. 设置自动备份脚本
cat > /etc/cron.d/accounting-backup << EOF
# 每天凌晨2点执行备份
0 2 * * * www-data /usr/bin/php /var/www/accounting-system/quick_backup.php full
EOF

# 2. 备份文件清理
find /var/www/accounting-system/Backup -name "*.sql" -mtime +7 -delete
find /var/www/accounting-system/Backup -name "*.tar.gz" -mtime +30 -delete
```

### 10.5 性能优化

#### 10.5.1 PHP优化
```ini
; php.ini 优化配置
memory_limit = 256M
max_execution_time = 60
max_input_vars = 3000
post_max_size = 32M
upload_max_filesize = 32M

; OPcache配置
opcache.enable = 1
opcache.memory_consumption = 128
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 60
```

#### 10.5.2 MySQL优化
```ini
# my.cnf 优化配置
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_size = 64M
query_cache_type = 1
max_connections = 200
```

#### 10.5.3 缓存优化
```bash
# 1. 启用Redis缓存（可选）
apt-get install redis-server
systemctl enable redis-server

# 2. 配置Nginx缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    gzip_static on;
}
```

### 10.6 安全加固

#### 10.6.1 文件权限
```bash
# 设置安全的文件权限
find /var/www/accounting-system -type f -exec chmod 644 {} \;
find /var/www/accounting-system -type d -exec chmod 755 {} \;
chmod 600 /var/www/accounting-system/.env
chmod 700 /var/www/accounting-system/logs
chmod 700 /var/www/accounting-system/Backup
```

#### 10.6.2 防火墙配置
```bash
# UFW防火墙配置
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable
```

#### 10.6.3 SSL证书配置
```bash
# 使用Let's Encrypt获取SSL证书
apt-get install certbot python3-certbot-nginx
certbot --nginx -d your-domain.com
```

---

## 总结

本文档提供了记账管理系统的完整技术规范，包括：

1. **项目概述和技术架构** - 明确了系统的目标、功能和技术选型
2. **功能模块详解** - 详细描述了各个功能模块的工作流程
3. **计算逻辑规范** - 精确定义了所有数学计算和业务算法
4. **数据结构定义** - 完整的数据库设计和数据模型
5. **API接口规范** - 标准化的接口文档和调用方式
6. **业务规则说明** - 明确的业务逻辑和处理规则
7. **配置参数说明** - 详细的系统配置和参数设置
8. **依赖关系图** - 清晰的模块依赖和数据流向
9. **部署和运维** - 完整的部署指南和运维方案

此文档确保了：
- **技术细节完整** - 包含足够的实现细节供开发参考
- **业务逻辑清晰** - 所有计算公式和业务规则都有精确描述
- **可重现性强** - 新开发人员可以基于此文档完全重现系统功能
- **维护性好** - 提供了完整的运维和监控方案

建议在项目重构过程中严格按照此规范执行，确保系统的稳定性和可维护性。
